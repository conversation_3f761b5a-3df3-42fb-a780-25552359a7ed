#!/usr/bin/env node

/**
 * Migration script for authentication improvements
 * This script applies the database schema changes for enhanced authentication
 */

const { PrismaClient } = require("@prisma/client");

const prisma = new PrismaClient();

async function main() {
  console.log("🚀 Starting authentication improvements migration...");

  try {
    // Check if AuditLog model exists by trying to count records
    try {
      await prisma.auditLog.count();
      console.log("✅ AuditLog model already exists");
    } catch (error) {
      console.log(
        "⚠️  AuditLog model does not exist yet - this is expected for first run"
      );
      console.log("   Please run: npx prisma db push");
      console.log("   This will apply the schema changes to your database");
      return;
    }

    // Create initial audit log entry to test the system
    await prisma.auditLog.create({
      data: {
        action: "SYSTEM_MIGRATION",
        resource: "database",
        ip: "localhost",
        userAgent: "migration-script",
        success: true,
        metadata: {
          migration: "auth-improvements",
          timestamp: new Date().toISOString(),
          version: "1.0.0",
        },
      },
    });

    console.log("✅ Created test audit log entry");

    // Check for any existing users without proper role assignments
    const allUsers = await prisma.user.findMany({
      select: {
        id: true,
        role: true,
        email: true,
      },
    });

    const usersWithoutRoles = allUsers.filter(
      (user) => !user.role || user.role === ""
    );

    if (usersWithoutRoles.length > 0) {
      console.log(
        `📝 Found ${usersWithoutRoles.length} users without proper roles`
      );

      // Update users without roles to have 'user' role
      for (const user of usersWithoutRoles) {
        await prisma.user.update({
          where: { id: user.id },
          data: { role: "user" },
        });
      }

      console.log('✅ Updated users to have default "user" role');
    }

    // Check for users with email/password but no emailVerified date
    const unverifiedUsers = await prisma.user.findMany({
      where: {
        AND: [{ password: { not: null } }, { emailVerified: null }],
      },
    });

    if (unverifiedUsers.length > 0) {
      console.log(
        `📝 Found ${unverifiedUsers.length} users with passwords but no email verification`
      );
      console.log(
        "   These users will need to verify their email before they can sign in"
      );
    }

    // Clean up old verification tokens (older than 24 hours)
    const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
    const deletedTokens = await prisma.verificationToken.deleteMany({
      where: {
        AND: [
          { expires: { lt: new Date() } },
          { createdAt: { lt: oneDayAgo } },
        ],
      },
    });

    if (deletedTokens.count > 0) {
      console.log(
        `🧹 Cleaned up ${deletedTokens.count} expired verification tokens`
      );
    }

    console.log(
      "✅ Authentication improvements migration completed successfully!"
    );
    console.log("\n📋 Summary of changes:");
    console.log("   • Added AuditLog model for security monitoring");
    console.log("   • Enhanced rate limiting with different tiers");
    console.log(
      "   • Added CredentialsProvider for email/password authentication"
    );
    console.log("   • Improved IP extraction and audit logging");
    console.log("   • Created custom login page at /login");
    console.log("\n🔧 Next steps:");
    console.log("   1. Test the new login page at /login");
    console.log("   2. Verify audit logs are being created in the database");
    console.log("   3. Test rate limiting on authentication endpoints");
    console.log("   4. Monitor security logs for any issues");
  } catch (error) {
    console.error("❌ Migration failed:", error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

main().catch((error) => {
  console.error("❌ Migration script failed:", error);
  process.exit(1);
});
